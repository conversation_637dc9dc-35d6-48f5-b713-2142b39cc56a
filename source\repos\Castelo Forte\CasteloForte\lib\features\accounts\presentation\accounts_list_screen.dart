import 'package:flutter/material.dart';

import '../../../core/utils/constants.dart';
import '../../../core/widgets/safe_back_button.dart';
import '../../../core/services/app_lifecycle_manager.dart';
import '../data/accounts_service.dart';
import '../data/models/account_model.dart';
import 'add_account_screen.dart';
import 'account_form_wrapper.dart';

/// Tela de listagem de contas seguindo o padrão das categorias
class AccountsListScreen extends StatefulWidget {
  const AccountsListScreen({super.key});

  @override
  State<AccountsListScreen> createState() => _AccountsListScreenState();
}

class _AccountsListScreenState extends State<AccountsListScreen>
    with WidgetsBindingObserver {
  final TextEditingController _searchController = TextEditingController();
  List<ContaViewModel> _filteredAccounts = [];
  bool _isLoading = true;
  String? _error;
  String _selectedFilter = 'TODAS';
  bool _hasBeenInitialized = false;

  final List<String> _filterOptions = ['TODAS', 'ATIVAS', 'INATIVAS'];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // Register the current route in the lifecycle manager
    AppLifecycleManager.instance.setCurrentRoute(AppConstants.accountsRoute);

    _loadAccounts();
    _searchController.addListener(_filterAccounts);
    _hasBeenInitialized = true;
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _searchController.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // Refresh data when app becomes active (resumed from background)
    if (state == AppLifecycleState.resumed && _hasBeenInitialized) {
      _refreshDataFromApi();
    }
  }

  Future<void> _loadAccounts() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Determina o filtro de status baseado na seleção atual
      bool? activeFilter;
      if (_selectedFilter == 'ATIVAS') {
        activeFilter = true;
      } else if (_selectedFilter == 'INATIVAS') {
        activeFilter = false;
      }
      // Para 'TODAS', activeFilter permanece null (busca todos)

      // Busca com filtros server-side
      final result = await AccountsService.getAccounts(
        search: _searchController.text.isNotEmpty
            ? _searchController.text
            : null,
        activeFilter: activeFilter,
      );

      setState(() {
        _filteredAccounts = result.accounts;
        _isLoading = false;
      });

      // Se há uma mensagem de erro mas ainda temos dados, mostra um aviso
      if (result.errorMessage != null && result.accounts.isNotEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result.errorMessage!),
              backgroundColor: Colors.orange,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      }
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
        _filteredAccounts = [];
      });
    }
  }

  void _filterAccounts() {
    // Agora que usamos filtros server-side, este método apenas dispara uma nova busca
    _refreshDataFromApi();
  }

  void _navigateToCreateAccount() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AddAccountScreen()),
    );
    if (result == true) {
      _loadAccounts();
    }
  }

  void _navigateToEditAccount(ContaViewModel account) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AccountFormWrapper(accountId: account.id),
      ),
    );
    if (result == true) {
      _loadAccounts();
    }
  }

  Future<void> _forceReload() async {
    await _loadAccounts();
  }

  /// Refreshes data from API - used for automatic refresh scenarios
  Future<void> _refreshDataFromApi() async {
    // Only refresh if the widget is still mounted and not already loading
    if (!mounted || _isLoading) return;

    await _loadAccounts();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A2E), // Mesmo fundo das categorias
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: SafeBackButton(fallbackRoute: AppConstants.dashboardRoute),
        title: const Text(
          'Contas',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.add, color: Colors.white),
            onPressed: _navigateToCreateAccount,
          ),
        ],
      ),
      body: _isLoading
          ? _buildLoadingState()
          : _error != null
          ? _buildErrorState()
          : Column(
              children: [
                // Barra de pesquisa
                Container(
                  margin: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: const Color(
                      0xFF16213E,
                    ), // Mesmo fundo das categorias
                    borderRadius: BorderRadius.circular(15),
                    border: Border.all(
                      color: const Color(0xFF4ECDC4).withValues(alpha: 0.3),
                    ),
                  ),
                  child: TextField(
                    controller: _searchController,
                    style: const TextStyle(color: Colors.white),
                    decoration: const InputDecoration(
                      hintText: 'Buscar contas...',
                      hintStyle: TextStyle(color: Colors.white70),
                      prefixIcon: Icon(Icons.search, color: Colors.white70),
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 15,
                      ),
                    ),
                  ),
                ),

                // Filtros por tipo
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: _filterOptions.map((filter) {
                      final isSelected = filter == _selectedFilter;
                      Color filterColor;
                      IconData filterIcon;

                      switch (filter) {
                        case 'ATIVAS':
                          filterColor = Colors.green;
                          filterIcon = Icons.check_circle;
                          break;
                        case 'INATIVAS':
                          filterColor = Colors.red;
                          filterIcon = Icons.cancel;
                          break;
                        case 'CORRENTE':
                          filterColor = Colors.blue;
                          filterIcon = Icons.account_balance;
                          break;
                        case 'POUPANÇA':
                          filterColor = Colors.orange;
                          filterIcon = Icons.savings;
                          break;
                        default:
                          filterColor = const Color(0xFF4ECDC4);
                          filterIcon = Icons.account_balance_wallet;
                      }

                      return Container(
                        margin: EdgeInsets.only(
                          left: filter == _filterOptions.first ? 20 : 8,
                          right: filter == _filterOptions.last ? 20 : 0,
                        ),
                        child: FilterChip(
                          selected: isSelected,
                          label: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                filterIcon,
                                size: 16,
                                color: isSelected ? Colors.white : filterColor,
                              ),
                              const SizedBox(width: 6),
                              Text(
                                filter,
                                style: TextStyle(
                                  color: isSelected
                                      ? Colors.white
                                      : filterColor,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                          onSelected: (selected) {
                            setState(() {
                              _selectedFilter = filter;
                            });
                            // Refresh data from API when filter changes
                            _refreshDataFromApi();
                          },
                          backgroundColor: Colors.transparent,
                          selectedColor: filterColor,
                          side: BorderSide(color: filterColor),
                        ),
                      );
                    }).toList(),
                  ),
                ),

                const SizedBox(height: 20),

                // Lista de contas
                Expanded(
                  child: _filteredAccounts.isEmpty
                      ? _buildEmptyState()
                      : RefreshIndicator(
                          onRefresh: _forceReload,
                          color: const Color(0xFF4ECDC4),
                          child: ListView.builder(
                            padding: const EdgeInsets.symmetric(horizontal: 20),
                            itemCount: _filteredAccounts.length,
                            itemBuilder: (context, index) {
                              final account = _filteredAccounts[index];
                              return _buildAccountCard(account);
                            },
                          ),
                        ),
                ),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _navigateToCreateAccount,
        backgroundColor: const Color(0xFF4ECDC4), // Cor padrão das categorias
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4ECDC4)),
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 80, color: Colors.red),
          const SizedBox(height: 20),
          Text(
            'Erro ao carregar contas',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.7),
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _error ?? 'Erro desconhecido',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.5),
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 30),
          ElevatedButton.icon(
            onPressed: _loadAccounts,
            icon: const Icon(Icons.refresh, color: Colors.white),
            label: const Text(
              'Tentar Novamente',
              style: TextStyle(color: Colors.white),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4ECDC4),
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.account_balance_wallet_outlined,
            size: 80,
            color: Colors.white.withValues(alpha: 0.3),
          ),
          const SizedBox(height: 20),
          Text(
            _searchController.text.isNotEmpty
                ? 'Nenhuma conta encontrada'
                : 'Nenhuma conta cadastrada',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.7),
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _searchController.text.isNotEmpty
                ? 'Tente buscar por outro termo'
                : 'Crie sua primeira conta',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.5),
              fontSize: 14,
            ),
          ),
          if (_searchController.text.isEmpty) ...[
            const SizedBox(height: 30),
            ElevatedButton.icon(
              onPressed: _navigateToCreateAccount,
              icon: const Icon(Icons.add, color: Colors.white),
              label: const Text(
                'Criar Conta',
                style: TextStyle(color: Colors.white),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF4ECDC4),
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildAccountCard(ContaViewModel account) {
    final accountColor = account.ativa ? const Color(0xFF4ECDC4) : Colors.grey;
    final accountIcon = account.isContaCorrente
        ? Icons.account_balance
        : Icons.savings;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Card(
        color: const Color(0xFF16213E), // Mesmo fundo dos cards das categorias
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15),
          side: BorderSide(
            color: accountColor.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: InkWell(
          onTap: () => _showAccountDetails(account),
          borderRadius: BorderRadius.circular(15),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Ícone da conta
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: accountColor.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(25),
                  ),
                  child: Icon(accountIcon, color: accountColor, size: 24),
                ),
                const SizedBox(width: 16),

                // Informações da conta
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        account.nomeExibicao,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        account.nomeBanco,
                        style: TextStyle(
                          color: Colors.white.withValues(alpha: 0.7),
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        account.tipoConta,
                        style: TextStyle(
                          color: accountColor,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),

                // Saldo e ações
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      account.saldoFormatado,
                      style: TextStyle(
                        color: account.saldo >= 0 ? Colors.green : Colors.red,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    PopupMenuButton<String>(
                      icon: Icon(
                        Icons.more_vert,
                        color: Colors.white.withValues(alpha: 0.7),
                      ),
                      color: const Color(0xFF16213E),
                      itemBuilder: (context) => [
                        PopupMenuItem(
                          value: 'edit',
                          child: Row(
                            children: [
                              Icon(Icons.edit, color: accountColor, size: 20),
                              const SizedBox(width: 8),
                              const Text(
                                'Editar',
                                style: TextStyle(color: Colors.white),
                              ),
                            ],
                          ),
                        ),
                        PopupMenuItem(
                          value: 'delete',
                          child: const Row(
                            children: [
                              Icon(Icons.delete, color: Colors.red, size: 20),
                              SizedBox(width: 8),
                              Text(
                                'Inativar',
                                style: TextStyle(color: Colors.red),
                              ),
                            ],
                          ),
                        ),
                      ],
                      onSelected: (value) {
                        if (value == 'edit') {
                          _navigateToEditAccount(account);
                        } else if (value == 'delete') {
                          _showDeleteConfirmation(account);
                        }
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showAccountDetails(ContaViewModel account) {
    final accountColor = account.ativa ? const Color(0xFF4ECDC4) : Colors.grey;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF16213E),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
        title: Row(
          children: [
            Icon(
              account.isContaCorrente ? Icons.account_balance : Icons.savings,
              color: accountColor,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                account.nomeExibicao,
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('Banco', account.nomeBanco),
            _buildDetailRow('Tipo', account.tipoConta),
            _buildDetailRow('Saldo', account.saldoFormatado),
            _buildDetailRow('Status', account.ativa ? 'Ativa' : 'Inativa'),
            if (account.apelido.isNotEmpty == true)
              _buildDetailRow('Apelido', account.apelido),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Fechar',
              style: TextStyle(color: Color(0xFF4ECDC4)),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 60,
            child: Text(
              '$label:',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.7),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(value, style: const TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(ContaViewModel account) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF16213E),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
        title: const Text(
          'Confirmar Inativação',
          style: TextStyle(color: Colors.white),
        ),
        content: Text(
          'Deseja inativar a conta "${account.nomeExibicao}"?',
          style: TextStyle(color: Colors.white.withValues(alpha: 0.7)),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Cancelar',
              style: TextStyle(color: Color(0xFF4ECDC4)),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteAccount(account);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Inativar'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteAccount(ContaViewModel account) async {
    try {
      final success = await AccountsService.deleteAccount(account.id);
      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Conta inativada com sucesso'),
              backgroundColor: Colors.green,
            ),
          );
          _loadAccounts();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Erro ao inativar conta'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erro ao inativar conta: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
